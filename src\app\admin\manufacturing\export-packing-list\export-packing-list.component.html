<div class="container mt-5">
  <div class="mb-2 d-flex justify-content-center text-primary-emphasis">
    <legend><b>Export Packing List</b></legend>
  </div>

  <div class="row mb-4 mt-4">
    <div class="col-12">
      <section>
        <fieldset>
          <form [formGroup]="carpetEntryForm">
            <div class="row mt-3">
              <div class="mt-3 container d-flex justify-content-between
              ">
              <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Invoice No.</mat-label>
                  <mat-select formControlName="invoiceNo" [disabled]="!availableInvoices.length">
      <mat-option *ngIf !="availableInvoices.length" [value]="null">
        Loading invoices...
      </mat-option>
      <mat-option *ngFor="let invoice of availableInvoices" [value]="invoice.id">
        {{invoice.invoiceNo}}
      </mat-option>
    </mat-select>
                </mat-form-field>
              </div>

              <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Date</mat-label>
                  <input matInput [matDatepicker]="picker" formControlName="date">
                  <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>
              </div>
                <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Area In</mat-label>
              <mat-select formControlName="AreaIn" >
                <mat-option value="Sq.Feet">Sq.Feet</mat-option>
                <mat-option value="Sq.Meter">Sq.Meter</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

              </div>

              <div class="mb-2 col-md-1">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Bale No</mat-label>
                  <input matInput formControlName="baleNo" readonly>
                  <mat-error *ngIf="carpetEntryForm.get('baleNo')?.hasError('required')">
                    Bale number is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="mb-2 col-md-1">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Pcs No.</mat-label>
                  <input matInput formControlName="pcsNo" readonly>
                </mat-form-field>
              </div>
              <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Carpet No.</mat-label>
                  <mat-select formControlName="carpetNo">
                    <mat-option *ngFor="let carpet of availableCarpets" [value]="carpet.carpetNo">
                      {{carpet.carpetNo}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="mb-2 quality-w">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Quality</mat-label>
                  <input matInput formControlName="quality" readonly>
                </mat-form-field>
              </div>
              <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Design</mat-label>
                  <input matInput formControlName="design" readonly>
                </mat-form-field>
              </div>
              <div class="mb-2 col-md-2">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Colour</mat-label>
                  <input matInput formControlName="colour" readonly>
                </mat-form-field>
              </div>
              <div class="mb-2 Size-w">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Size</mat-label>
                  <input matInput formControlName="size">
                </mat-form-field>
              </div>
              <div class="mb-2 area-w">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Area</mat-label>
                  <input matInput formControlName="area" readonly>
                </mat-form-field>
              </div>
              <div class="mb-2 col-md-3">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Remarks</mat-label>
                  <textarea matInput formControlName="remarks" placeholder="Add remarks" rows="2"></textarea>
                </mat-form-field>
              </div>
              <div class="mt-2 col-md-1">
                <button mat-flat-button color="primary" class="w-100" (click)="addCarpetEntry()">Add</button>
              </div>
            </div>
          </form>
        </fieldset>
      </section>
    </div>
  </div>

  <!-- Table to display added items -->
  <div class="mt-5">
    <section>
      <fieldset>
        <legend><b>Bale No. {{currentBaleNo}}</b></legend>
        <div class="row">
          <div class="col-12">
            <table mat-table [dataSource]="getCurrentBaleItems()" matSort>
              <ng-container matColumnDef="pcsNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs No.</th>
                <td mat-cell *matCellDef="let element">{{element.pcsNo}}</td>
              </ng-container>
              <ng-container matColumnDef="carpetNo">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Carpet No.</th>
                <td mat-cell *matCellDef="let element">{{element.carpetNo}}</td>
              </ng-container>
              <ng-container matColumnDef="quality">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                <td mat-cell *matCellDef="let element">{{element.quality}}</td>
              </ng-container>
              <ng-container matColumnDef="design">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
                <td mat-cell *matCellDef="let element">{{element.design}}</td>
              </ng-container>
              <ng-container matColumnDef="colour">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Colour</th>
                <td mat-cell *matCellDef="let element">{{element.colour}}</td>
              </ng-container>
              <ng-container matColumnDef="size">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Size</th>
                <td mat-cell *matCellDef="let element">{{element.size}}</td>
              </ng-container>
              <ng-container matColumnDef="area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                <td mat-cell *matCellDef="let element">
                  <span *ngIf="element.areaDisplay">{{element.areaDisplay}}</span>
                  <span *ngIf="!element.areaDisplay && element.areaIn === 'Sq.Feet'">
                    {{element.area}}
                  </span>
                  <span *ngIf="!element.areaDisplay && element.areaIn === 'Sq.Yard'">
                    {{element.area}}
                  </span>
                </td>
              </ng-container>
              <ng-container matColumnDef="remarks">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>Remarks</th>
                <td mat-cell *matCellDef="let element">{{element.remarks}}</td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <div *ngIf="getCurrentBaleItems().length > 0">
              <hr class="mt-3 mb-3" style="border-top: 2px solid #000000; width: 100%;">
              <div class="d-flex justify-content-between">
                <div>
                  <div class="d-flex">
                    <div class="me-4 p-2 bg-light border">
                      <span class="font-weight-medium">Total Pieces: {{getCurrentBaleItems().length}}</span>
                    </div>
                    <div class="p-2 bg-light border">
                      <span class="font-weight-medium">Total Area: {{getTotalAreaForCurrentBale() | number:'1.2-2'}}</span>
                    </div>
                  </div>
                </div>
                <div class="col-md-1 save-m">
                  <button mat-flat-button color="primary" (click)="savePackingList()">
                    <mat-icon>save</mat-icon> Save
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </fieldset>
    </section>
  </div>

  <!-- Table to display completed bales -->
  <div class="mt-5">
    <section>
      <fieldset>
        <legend><b>Bale List</b></legend>
        <div class="row">
          <div class="col-12">
            <div class="mat-elevation-z8" style="overflow: auto;">
              <table mat-table [dataSource]="completedBalesDataSource" matSort class="w-100">
                <ng-container matColumnDef="SrNo">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header> Sr.No </th>
                  <td mat-cell *matCellDef="let element">
                    <span *ngIf="element.SrNo > 0">{{element.SrNo}}</span>
                  </td>
                </ng-container>
                <ng-container matColumnDef="baleNo">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Bale No</th>
                  <td mat-cell *matCellDef="let element">{{element.baleNo}}</td>
                </ng-container>
                <ng-container matColumnDef="pcsNo">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Pcs No.</th>
                  <td mat-cell *matCellDef="let element">{{element.pcsNo}}</td>
                </ng-container>
                <ng-container matColumnDef="carpetNo">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Carpet No.</th>
                  <td mat-cell *matCellDef="let element">{{element.carpetNo}}</td>
                </ng-container>
                <ng-container matColumnDef="quality">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Quality</th>
                  <td mat-cell *matCellDef="let element">{{element.quality}}</td>
                </ng-container>
                <ng-container matColumnDef="design">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Design</th>
                  <td mat-cell *matCellDef="let element">{{element.design}}</td>
                </ng-container>
                <ng-container matColumnDef="colour">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Colour</th>
                  <td mat-cell *matCellDef="let element">{{element.colour}}</td>
                </ng-container>
                <ng-container matColumnDef="size">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Size</th>
                  <td mat-cell *matCellDef="let element">{{element.size}}</td>
                </ng-container>
                <ng-container matColumnDef="area">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Area</th>
                  <td mat-cell *matCellDef="let element">
                    <span *ngIf="element.areaDisplay">{{element.areaDisplay}}</span>
                    <span *ngIf="!element.areaDisplay && element.areaIn === 'Sq.Feet'">{{element.area}} Ft</span>
                    <span *ngIf="!element.areaDisplay && element.areaIn === 'Sq.Yard'">{{element.area}} Yd</span>
                  </td>
                </ng-container>
                <ng-container matColumnDef="tArea">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>T. Area</th>
                  <td mat-cell *matCellDef="let element">
                    <span *ngIf="element.isLastInBale">
                      {{getTotalAreaForBale(element.baleNo) | number:'1.2-2'}}
                    </span>
                  </td>
                </ng-container>
                <ng-container matColumnDef="remarks">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header>Remarks</th>
                  <td mat-cell *matCellDef="let element">{{element.remarks}}</td>
                </ng-container>
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let element">
                    <button *ngIf="element.isLastInBale" mat-icon-button color="primary" (click)="editBale(element)" matTooltip="Edit Bale">
                      <mat-icon>edit</mat-icon>
                    </button>
                  </td>
                </ng-container>
                <tr mat-header-row *matHeaderRowDef="completedBaleColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: completedBaleColumns;"></tr>
              </table>
              <mat-paginator #completedBalesPaginator [pageSizeOptions]="[10, 5, 25, 100]" aria-label="Select page of bales"></mat-paginator>
            </div>
          </div>
        </div>
      </fieldset>
    </section>
  </div>
</div>